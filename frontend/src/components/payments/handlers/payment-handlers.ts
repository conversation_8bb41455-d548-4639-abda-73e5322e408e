import { toast } from "sonner";
import { processTokenPayment } from "../../../services/api";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import { postMessageToParent } from "../utils/iframe-communication";

export const createPaymentSuccessHandler = (
  config: PayFieldsConfig,
  paymentInfo: PaymentInfo | null,
  billingAddress?: BillingAddress,
  onSuccess?: (response: unknown) => void
) => {
  return async (response: PaymentResponse) => {
    // Check if response is null or undefined
    if (!response) {
      throw new Error("Payment response is null or undefined");
    }

    let extractedToken: string | undefined;
    let extractedTokenId: string | undefined;

    if (config.mode === "token") {
      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        const firstDataItem = response.data[0];
        extractedTokenId = firstDataItem?.id;
        extractedToken = firstDataItem?.token;
      } else if (response.token) {
        extractedToken = response.token;
        extractedTokenId = response.id || response.token;
      } else if (response.details) {
        extractedToken = response.details.token;
        extractedTokenId = response.details.id || response.details.token;
      }
    }

    if (config.mode === "token" && extractedToken) {
      const actualAmount = paymentInfo?.amount || config.amount;

      try {
        const paymentResult = await processTokenPayment({
          merchantId: config.merchantId,
          token: extractedToken,
          tokenId: extractedTokenId || extractedToken,
          amount: actualAmount,
          description: paymentInfo?.description || config.description,
          customerInfo: billingAddress
            ? {
                name: `${billingAddress.firstName} ${billingAddress.lastName}`,
                email: billingAddress.email,
                address: {
                  line1: billingAddress.line1,
                  line2: billingAddress.line2,
                  city: billingAddress.city,
                  state: billingAddress.state,
                  zip: billingAddress.zip,
                  country: billingAddress.country,
                },
              }
            : undefined,
        });

        if (paymentResult.success) {
          toast.success("Payment processed successfully!");

          postMessageToParent("PAYMENT_SUCCESS", {
            data: {
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
              tokenProcessed: true,
            },
          });

          if (onSuccess) {
            onSuccess({
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
              tokenProcessed: true,
            });
          }
        } else {
          throw new Error(paymentResult.message || "Token payment processing failed");
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Token payment processing failed";
        toast.error(`Payment failed: ${errorMessage}`);

        postMessageToParent("PAYMENT_ERROR", {
          error: errorMessage,
          tokenGenerated: true,
          tokenProcessingFailed: true,
        });

        if (onSuccess) {
          onSuccess({
            error: errorMessage,
            tokenGenerated: true,
            tokenProcessingFailed: true,
          });
        }
        throw error;
      }
    } else if (config.mode === "token" && !extractedToken) {
      const errorMessage = "Token generation failed: No token received from PayFields";

      toast.error(errorMessage);
      postMessageToParent("PAYMENT_ERROR", {
        error: errorMessage,
        tokenGenerationFailed: true,
      });

      if (onSuccess) {
        onSuccess({
          error: errorMessage,
          tokenGenerationFailed: true,
        });
      }
      throw new Error(errorMessage);
    } else {
      toast.success("Payment processed successfully!");
      postMessageToParent("PAYMENT_SUCCESS", { data: response });
      if (onSuccess) onSuccess(response);
    }
  };
};

const isGooglePayCancellation = (err: PaymentError): boolean => {
  if (!err) return false;

  // Check for statusCode property indicating cancellation (Google Pay specific)
  if (err.statusCode === "CANCELED" || err.statusCode === "CANCELLED") {
    return true;
  }

  // Check message-based cancellation patterns
  if (!err.message) return false;

  const message = err.message.toLowerCase();
  const cancellationKeywords = [
    "canceled",
    "cancelled",
    "user canceled",
    "user cancelled",
    "user_canceled",
    "user_cancelled",
    "aborted",
    "dismissed",
    "closed",
    "user closed",
    "user dismissed",
    "payment_canceled",
    "payment_cancelled",
    "google pay canceled",
    "google pay cancelled",
  ];

  return cancellationKeywords.some((keyword) => message.includes(keyword));
};

export const createPaymentFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: PaymentError) => {
    // Check if this is a Google Pay user cancellation
    if (isGooglePayCancellation(err)) {
      // Don't show error toast for user cancellations
      postMessageToParent("PAYMENT_CANCELLED", {
        message: "Payment cancelled by user",
        details: err,
      });

      // Don't call onFailure for user cancellations to avoid error states
      return;
    }

    // Handle actual payment errors
    let errorMessage = "Payment processing failed. Please try again.";
    if (err && Array.isArray(err.errors)) {
      const fieldErrors = err.errors.map((e) => `${e.field}: ${e.msg}`).join(", ");
      errorMessage = `Payment failed: ${fieldErrors}`;
    } else if (err && err.message) {
      errorMessage = err.message;
    }

    toast.error(errorMessage);
    postMessageToParent("PAYMENT_FAILURE", {
      error: errorMessage,
      details: err,
    });

    if (onFailure) onFailure(err);
  };
};

export const createValidationFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: unknown) => {
    const validationMessage = "Payment validation failed. Please check your card details.";
    toast.error("Please check your card details");

    postMessageToParent("PAYMENT_VALIDATION_FAILURE", {
      error: validationMessage,
      details: err,
    });

    if (onFailure) onFailure({ message: validationMessage, details: err });
  };
};

export const createPaymentFinishHandler = () => {
  return (response: unknown) => {
    postMessageToParent("PAYMENT_FINISHED", { data: response });
  };
};
