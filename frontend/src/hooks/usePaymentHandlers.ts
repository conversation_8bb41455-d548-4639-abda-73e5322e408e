import { useState } from "react";
import { toast } from "sonner";
import { formatSuccessMessage, formatErrorMessage } from "../utils/paymentUtils";

interface UsePaymentHandlersReturn {
  success: boolean;
  error: string | null;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  handlePaymentSuccess: (response: unknown) => void;
  handlePaymentFailure: (error: unknown) => void;
}

export const usePaymentHandlers = (): UsePaymentHandlersReturn => {
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (response: unknown) => {
    setSuccess(true);
    toast.success("Payment processed successfully!");

    if (window.parent !== window) {
      window.parent.postMessage(formatSuccessMessage(response, "PAYMENT_SUCCESS"), "*");
    }
  };

  const handlePaymentFailure = (error: unknown) => {
    const err = error as { statusCode?: string; message?: string };
    if (err?.statusCode === "CANCELED" || err?.statusCode === "CANCELLED") {
      return;
    }

    const errorMessage = error instanceof Error ? error.message : (error as { message?: string })?.message || "Payment processing failed";

    setError(errorMessage);
    toast.error(errorMessage);

    if (window.parent !== window) {
      window.parent.postMessage(formatErrorMessage(error, "PAYMENT_FAILURE"), "*");
    }
  };

  return {
    success,
    error,
    setError,
    handlePaymentSuccess,
    handlePaymentFailure,
  };
};
